<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Signature Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .signature-canvas {
            border: 2px solid #333;
            cursor: crosshair;
        }
        button {
            margin: 5px;
            padding: 10px 15px;
            background: #007bff;
            color: white;
            border: none;
            border-radius: 3px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .clear-btn {
            background: #dc3545;
        }
        .clear-btn:hover {
            background: #c82333;
        }
        pre {
            background: #f8f9fa;
            padding: 10px;
            border-radius: 3px;
            overflow-x: auto;
            max-height: 200px;
        }
        .signature-preview {
            max-width: 200px;
            border: 1px solid #ddd;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <h1>Signature Test for Onderhoudsbon</h1>
    
    <div class="test-section">
        <h2>1. Create Test Signatures</h2>
        <p>Draw signatures below to test the functionality:</p>
        
        <h3>Klant Handtekening</h3>
        <canvas id="klantCanvas" class="signature-canvas" width="300" height="150"></canvas>
        <br>
        <button onclick="clearCanvas('klantCanvas')">Clear Klant Signature</button>
        
        <h3>Monteur Handtekening</h3>
        <canvas id="monteurCanvas" class="signature-canvas" width="300" height="150"></canvas>
        <br>
        <button onclick="clearCanvas('monteurCanvas')">Clear Monteur Signature</button>
    </div>
    
    <div class="test-section">
        <h2>2. Test Signature Processing</h2>
        <button onclick="testSignatureProcessing()">Test Signature Processing</button>
        <button onclick="testDocumentGeneration()">Test Document Generation</button>
        <div id="testResults"></div>
    </div>
    
    <div class="test-section">
        <h2>3. Generated Signatures Preview</h2>
        <div id="signaturePreview"></div>
    </div>

    <script>
        // Simple signature drawing functionality
        let isDrawing = false;
        let currentCanvas = null;
        let currentContext = null;

        function setupCanvas(canvasId) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            canvas.addEventListener('mousedown', startDrawing);
            canvas.addEventListener('mousemove', draw);
            canvas.addEventListener('mouseup', stopDrawing);
            canvas.addEventListener('mouseout', stopDrawing);
            
            function startDrawing(e) {
                isDrawing = true;
                currentCanvas = canvas;
                currentContext = ctx;
                const rect = canvas.getBoundingClientRect();
                ctx.beginPath();
                ctx.moveTo(e.clientX - rect.left, e.clientY - rect.top);
            }
            
            function draw(e) {
                if (!isDrawing || currentCanvas !== canvas) return;
                const rect = canvas.getBoundingClientRect();
                ctx.lineWidth = 2;
                ctx.lineCap = 'round';
                ctx.strokeStyle = '#000';
                ctx.lineTo(e.clientX - rect.left, e.clientY - rect.top);
                ctx.stroke();
                ctx.beginPath();
                ctx.moveTo(e.clientX - rect.left, e.clientY - rect.top);
            }
            
            function stopDrawing() {
                if (currentCanvas === canvas) {
                    isDrawing = false;
                    currentCanvas = null;
                    currentContext = null;
                }
            }
        }

        function clearCanvas(canvasId) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            ctx.clearRect(0, 0, canvas.width, canvas.height);
        }

        function getSignatureData(canvasId) {
            const canvas = document.getElementById(canvasId);
            return canvas.toDataURL('image/png');
        }

        function testSignatureProcessing() {
            const klantSignature = getSignatureData('klantCanvas');
            const monteurSignature = getSignatureData('monteurCanvas');
            
            const testData = {
                klant_naam: 'Test Klant',
                datum: '2025-01-14',
                klant_handtekening: klantSignature,
                monteur_naam: 'Test Monteur',
                begin_tijd: '09:00',
                eind_tijd: '17:00',
                monteur_handtekening: monteurSignature,
                installatie_in_orde: true,
                ja_opmerking: 'Test opmerking'
            };
            
            // Test signature validation
            const isKlantValid = klantSignature.startsWith('data:image/');
            const isMonteurValid = monteurSignature.startsWith('data:image/');
            
            const results = {
                klantSignatureValid: isKlantValid,
                monteurSignatureValid: isMonteurValid,
                klantSignatureLength: klantSignature.length,
                monteurSignatureLength: monteurSignature.length,
                testData: testData
            };
            
            document.getElementById('testResults').innerHTML = `
                <h3>Test Results:</h3>
                <pre>${JSON.stringify(results, null, 2)}</pre>
            `;
            
            // Show signature previews
            document.getElementById('signaturePreview').innerHTML = `
                <h3>Klant Signature:</h3>
                <img src="${klantSignature}" class="signature-preview" alt="Klant Signature">
                <h3>Monteur Signature:</h3>
                <img src="${monteurSignature}" class="signature-preview" alt="Monteur Signature">
            `;
            
            return results;
        }

        function testDocumentGeneration() {
            const results = testSignatureProcessing();
            
            // Simulate the signature processing that would happen in the real app
            const processedData = { ...results.testData };
            
            // Convert signatures to image objects (simulating processSignaturesForTemplate)
            if (results.klantSignatureValid) {
                processedData.klant_handtekening = {
                    _type: 'image',
                    buffer: 'ArrayBuffer would be here',
                    width: 150,
                    height: 75,
                    extension: '.png'
                };
            } else {
                processedData.klant_handtekening = 'Niet ondertekend';
            }
            
            if (results.monteurSignatureValid) {
                processedData.monteur_handtekening = {
                    _type: 'image',
                    buffer: 'ArrayBuffer would be here',
                    width: 150,
                    height: 75,
                    extension: '.png'
                };
            } else {
                processedData.monteur_handtekening = 'Niet ondertekend';
            }
            
            document.getElementById('testResults').innerHTML += `
                <h3>Processed Data for Document Generation:</h3>
                <pre>${JSON.stringify(processedData, null, 2)}</pre>
            `;
            
            console.log('Document generation test completed');
            console.log('Processed data:', processedData);
        }

        // Initialize canvases when page loads
        window.onload = function() {
            setupCanvas('klantCanvas');
            setupCanvas('monteurCanvas');
        };
    </script>
</body>
</html>
