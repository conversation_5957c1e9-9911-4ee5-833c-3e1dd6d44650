/**
 * Test file to verify signature functionality in document generation
 */

import { processSignaturesForTemplate, isBase64Signature, base64ToBuffer } from '../utils/signatureUtils';

// Sample base64 signature data (minimal PNG)
const sampleSignature = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAYAAAAfFcSJAAAADUlEQVR42mNkYPhfDwAChAI9jU77zgAAAABJRU5ErkJggg==';

// Test data that would come from a form
const testData = {
  klant_naam: 'Test Klant',
  datum: '2025-01-14',
  klant_handtekening: sampleSignature,
  monteur_naam: 'Test Monteur',
  begin_tijd: '09:00',
  eind_tijd: '17:00',
  monteur_handtekening: sampleSignature,
  installatie_in_orde: true,
  ja_opmerking: 'Alles werkt goed'
};

export function testSignatureProcessing() {
  console.log('🧪 Testing signature processing...');
  
  // Test 1: Check if base64 signature is detected correctly
  console.log('Test 1: Base64 signature detection');
  console.log('Is valid signature:', isBase64Signature(sampleSignature));
  console.log('Is invalid signature:', isBase64Signature('not a signature'));
  
  // Test 2: Test base64 to buffer conversion
  console.log('\nTest 2: Base64 to buffer conversion');
  try {
    const buffer = base64ToBuffer(sampleSignature);
    console.log('Buffer created successfully, length:', buffer.byteLength);
  } catch (error) {
    console.error('Buffer conversion failed:', error);
  }
  
  // Test 3: Test signature processing for template
  console.log('\nTest 3: Signature processing for template');
  try {
    const processedData = processSignaturesForTemplate(testData);
    console.log('Processed data keys:', Object.keys(processedData));
    
    // Check if signatures were converted to image objects
    if (processedData.klant_handtekening && typeof processedData.klant_handtekening === 'object') {
      console.log('✅ klant_handtekening converted to image object');
      console.log('Image object properties:', Object.keys(processedData.klant_handtekening));
    } else {
      console.log('❌ klant_handtekening not converted properly');
    }
    
    if (processedData.monteur_handtekening && typeof processedData.monteur_handtekening === 'object') {
      console.log('✅ monteur_handtekening converted to image object');
      console.log('Image object properties:', Object.keys(processedData.monteur_handtekening));
    } else {
      console.log('❌ monteur_handtekening not converted properly');
    }
    
    return processedData;
  } catch (error) {
    console.error('Signature processing failed:', error);
    return null;
  }
}

// Test with empty signatures
export function testEmptySignatures() {
  console.log('\n🧪 Testing empty signature handling...');
  
  const emptyTestData = {
    klant_naam: 'Test Klant',
    datum: '2025-01-14',
    klant_handtekening: '', // Empty signature
    monteur_naam: 'Test Monteur',
    monteur_handtekening: null, // Null signature
  };
  
  try {
    const processedData = processSignaturesForTemplate(emptyTestData);
    console.log('Empty signature results:');
    console.log('klant_handtekening:', processedData.klant_handtekening);
    console.log('monteur_handtekening:', processedData.monteur_handtekening);
    
    return processedData;
  } catch (error) {
    console.error('Empty signature processing failed:', error);
    return null;
  }
}

// Run tests if this file is executed directly
if (typeof window !== 'undefined') {
  // Browser environment
  console.log('Running signature tests in browser...');
  testSignatureProcessing();
  testEmptySignatures();
}
